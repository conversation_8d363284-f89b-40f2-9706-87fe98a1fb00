"""
Franchisor Service - Business Logic Layer
Production-grade service implementation using repository pattern
"""

from typing import Op<PERSON>, List, Tuple
from fastapi import HTT<PERSON>Exception
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.repositories.franchisor_repository import FranchisorRepository
from app.core.utils.exception_manager.custom_exceptions import (
    ValidationError,
    DatabaseError,
    ResourceNotFoundError
)
from app.schemas.franchisor import (
    FranchisorCreateRequest,
    FranchisorUpdateRequest,
    FranchisorResponse
)
from app.schemas.industry import IndustryResponse
from app.models.franchisor import Franchisor
from app.core.logging import logger


class FranchisorService:
    """
    Franchisor service implementing business logic with repository pattern.
    Focuses on business rules, validation, and response formatting.
    """
    
    def __init__(self, repository: FranchisorRepository):
        """
        Initialize service with repository dependency injection.
        
        Args:
            repository: FranchisorRepository instance
        """
        self.repository = repository
        
        # Initialize S3 service for file uploads
        try:
            from app.services.s3_service import S3Service
            self.s3_service = S3Service()
        except Exception as e:
            logger.warning(f"S3 service initialization failed: {e}")
            self.s3_service = None
    
    async def create_franchisor(self, franchisor_data: FranchisorCreateRequest) -> Franchisor:
        """
        Create a new franchisor with business validation.

        Args:
            franchisor_data: Franchisor creation data

        Returns:
            Created franchisor model

        Raises:
            ValidationError: If input validation fails
            DatabaseError: If database operation fails
        """
        try:
            # Validate required fields
            if not franchisor_data.name or not franchisor_data.name.strip():
                raise ValidationError(
                    error_key="INVALID_FRANCHISOR_NAME",
                    message="Franchisor name is required and cannot be empty"
                )

            # Validate industry_id format if provided
            if franchisor_data.industry_id:
                try:
                    uuid.UUID(franchisor_data.industry_id)
                except ValueError:
                    raise ValidationError(
                        error_key="INVALID_INDUSTRY_ID_FORMAT",
                        message="Industry ID must be a valid UUID format"
                    )

            # Validate email format if provided
            if franchisor_data.email:
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, franchisor_data.email):
                    raise ValidationError(
                        error_key="INVALID_EMAIL_FORMAT",
                        message="Invalid email format provided"
                    )

            # Validate phone format if provided
            if franchisor_data.phone:
                # Remove common phone formatting characters
                phone_clean = re.sub(r'[^\d+]', '', franchisor_data.phone)
                if len(phone_clean) < 10 or len(phone_clean) > 15:
                    raise ValidationError(
                        error_key="INVALID_PHONE_FORMAT",
                        message="Phone number must be between 10-15 digits"
                    )

            # Create franchisor using repository
            franchisor = await self.repository.create(franchisor_data)
            logger.info(f"Successfully created franchisor: {franchisor.id} - {franchisor.name}")
            return franchisor

        except ValidationError:
            raise
        except IntegrityError as ie:
            logger.error(f"Database integrity error creating franchisor: {ie}")
            if "unique constraint" in str(ie).lower():
                raise DatabaseError(
                    error_key="DUPLICATE_FRANCHISOR",
                    message="A franchisor with this information already exists"
                )
            elif "foreign key constraint" in str(ie).lower():
                raise DatabaseError(
                    error_key="INVALID_FOREIGN_KEY",
                    message="Invalid industry ID provided"
                )
            else:
                raise DatabaseError(
                    error_key="DATABASE_INTEGRITY_ERROR",
                    message="Database integrity constraint violation"
                )
        except SQLAlchemyError as db_error:
            logger.error(f"Database error creating franchisor: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_OPERATION_FAILED",
                message="Failed to create franchisor due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error creating franchisor: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISOR_CREATION_FAILED",
                message=f"An unexpected error occurred while creating franchisor: {str(e)}"
            )
    
    async def get_franchisor_by_id(self, franchisor_id: str, include_deleted: bool = False) -> Optional[FranchisorResponse]:
        """
        Get franchisor by ID with formatted response.

        Args:
            franchisor_id: Franchisor UUID
            include_deleted: Whether to include soft-deleted records

        Returns:
            Formatted franchisor response or None

        Raises:
            ValidationError: If franchisor_id format is invalid
            DatabaseError: If database operation fails
        """
        try:
            # Validate UUID format
            try:
                uuid.UUID(franchisor_id)
            except ValueError:
                raise ValidationError(
                    error_key="INVALID_FRANCHISOR_ID",
                    message="Franchisor ID must be a valid UUID format"
                )

            franchisor = await self.repository.get_by_id_with_relations(franchisor_id, include_deleted)
            if not franchisor:
                return None

            return await self._format_franchisor_response(franchisor)

        except ValidationError:
            raise
        except SQLAlchemyError as db_error:
            logger.error(f"Database error retrieving franchisor {franchisor_id}: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_OPERATION_FAILED",
                message="Failed to retrieve franchisor due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error retrieving franchisor {franchisor_id}: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISOR_RETRIEVAL_FAILED",
                message=f"An unexpected error occurred while retrieving franchisor: {str(e)}"
            )
    
    async def get_franchisors(
        self,
        skip: int = 0,
        limit: int = 20,
        industry: Optional[str] = None,
        region: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "asc"
    ) -> Tuple[List[FranchisorResponse], int]:
        """
        Get franchisors with filtering and pagination.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            industry: Filter by industry name
            region: Filter by region
            is_active: Filter by active status
            search: Search term for name
            sort_by: Field to sort by (name, contactfirstname, contactlastname, franchisor_won_id, industry_name, is_active)
            sort_order: Sort order (asc or desc)

        Returns:
            Tuple of (formatted franchisor responses, total count)
        """
        try:
            # Validate pagination parameters
            if skip < 0:
                raise ValidationError(
                    error_key="INVALID_SKIP_PARAMETER",
                    message="Skip parameter must be non-negative"
                )

            if limit <= 0 or limit > 1000:
                raise ValidationError(
                    error_key="INVALID_LIMIT_PARAMETER",
                    message="Limit parameter must be between 1 and 1000"
                )

            # Validate sort parameters
            valid_sort_fields = ["name", "contactfirstname", "contactlastname", "franchisor_won_id", "industry_name", "is_active", "region", "budget", "created_at"]
            if sort_by and sort_by not in valid_sort_fields:
                raise ValidationError(
                    error_key="INVALID_SORT_FIELD",
                    message=f"Sort field must be one of: {', '.join(valid_sort_fields)}"
                )

            if sort_order and sort_order.lower() not in ["asc", "desc"]:
                raise ValidationError(
                    error_key="INVALID_SORT_ORDER",
                    message="Sort order must be 'asc' or 'desc'"
                )

            franchisors, total_count = await self.repository.get_multi_with_filters(
                skip=skip,
                limit=limit,
                industry=industry,
                region=region,
                is_active=is_active,
                search=search,
                sort_by=sort_by,
                sort_order=sort_order
            )

            # Format responses
            formatted_franchisors = []
            for franchisor in franchisors:
                formatted_response = await self._format_franchisor_response(franchisor)
                formatted_franchisors.append(formatted_response)

            logger.info(f"Retrieved {len(formatted_franchisors)} franchisors out of {total_count} total")
            return formatted_franchisors, total_count

        except ValidationError:
            raise
        except SQLAlchemyError as db_error:
            logger.error(f"Database error retrieving franchisors: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_OPERATION_FAILED",
                message="Failed to retrieve franchisors due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error retrieving franchisors: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISORS_RETRIEVAL_FAILED",
                message=f"An unexpected error occurred while retrieving franchisors: {str(e)}"
            )
    
    async def update_franchisor(
        self, 
        franchisor_id: str, 
        franchisor_data: FranchisorUpdateRequest, 
        brochure_file=None
    ) -> Optional[FranchisorResponse]:
        """
        Update franchisor with business validation and optional brochure upload.
        
        Args:
            franchisor_id: Franchisor UUID
            franchisor_data: Update data
            brochure_file: Optional brochure file
            
        Returns:
            Updated franchisor response or None if not found
        """
        try:
            # Validate UUID format
            try:
                uuid.UUID(franchisor_id)
            except ValueError:
                raise ValidationError(
                    error_key="INVALID_FRANCHISOR_ID",
                    message="Franchisor ID must be a valid UUID format"
                )

            # Get existing franchisor
            existing_franchisor = await self.repository.get(franchisor_id)
            if not existing_franchisor:
                return None

            # Validate update data if provided
            if franchisor_data.name is not None and not franchisor_data.name.strip():
                raise ValidationError(
                    error_key="INVALID_FRANCHISOR_NAME",
                    message="Franchisor name cannot be empty"
                )

            # Validate industry_id format if provided
            if franchisor_data.industry_id:
                try:
                    uuid.UUID(franchisor_data.industry_id)
                except ValueError:
                    raise ValidationError(
                        error_key="INVALID_INDUSTRY_ID_FORMAT",
                        message="Industry ID must be a valid UUID format"
                    )

            # Validate email format if provided
            if franchisor_data.email:
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, franchisor_data.email):
                    raise ValidationError(
                        error_key="INVALID_EMAIL_FORMAT",
                        message="Invalid email format provided"
                    )

            # Validate phone format if provided
            if franchisor_data.phone:
                import re
                phone_clean = re.sub(r'[^\d+]', '', franchisor_data.phone)
                if len(phone_clean) < 10 or len(phone_clean) > 15:
                    raise ValidationError(
                        error_key="INVALID_PHONE_FORMAT",
                        message="Phone number must be between 10-15 digits"
                    )

            # Handle brochure upload if provided
            if brochure_file and self.s3_service:
                try:
                    brochure_filename = await self.s3_service.upload_file(brochure_file, prefix="brochures")
                    # Update brochure URL using repository
                    await self.repository.update_brochure_url(franchisor_id, brochure_filename)
                    logger.info(f"Uploaded brochure for franchisor {franchisor_id}: {brochure_filename}")

                    # Process document with agent system for ingestion
                    await self._process_brochure_with_agents(franchisor_id, brochure_filename)

                except Exception as e:
                    logger.error(f"Failed to upload brochure for franchisor {franchisor_id}: {e}")
                    # Continue with update even if brochure upload fails

            # Update franchisor using repository
            updated_franchisor = await self.repository.update(existing_franchisor, franchisor_data)

            # Return formatted response
            return await self._format_franchisor_response(updated_franchisor)

        except ValidationError:
            raise
        except IntegrityError as ie:
            logger.error(f"Database integrity error updating franchisor {franchisor_id}: {ie}")
            if "unique constraint" in str(ie).lower():
                raise DatabaseError(
                    error_key="DUPLICATE_FRANCHISOR",
                    message="A franchisor with this information already exists"
                )
            elif "foreign key constraint" in str(ie).lower():
                raise DatabaseError(
                    error_key="INVALID_FOREIGN_KEY",
                    message="Invalid industry ID provided"
                )
            else:
                raise DatabaseError(
                    error_key="DATABASE_INTEGRITY_ERROR",
                    message="Database integrity constraint violation"
                )
        except SQLAlchemyError as db_error:
            logger.error(f"Database error updating franchisor {franchisor_id}: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_OPERATION_FAILED",
                message="Failed to update franchisor due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error updating franchisor {franchisor_id}: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISOR_UPDATE_FAILED",
                message=f"An unexpected error occurred while updating franchisor: {str(e)}"
            )

    async def update_brochure_url(self, franchisor_id: str, brochure_filename: str) -> Optional[FranchisorResponse]:
        """
        Update franchisor brochure URL.

        Args:
            franchisor_id: Franchisor UUID
            brochure_filename: S3 filename for the brochure

        Returns:
            Updated franchisor response or None if not found
        """
        try:
            # Update brochure URL using repository
            updated_franchisor = await self.repository.update_brochure_url(franchisor_id, brochure_filename)
            if not updated_franchisor:
                return None

            return await self._format_franchisor_response(updated_franchisor)

        except Exception as e:
            logger.error(f"Error updating brochure URL for franchisor {franchisor_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to update brochure URL")
    
    async def activate_franchisor(self, franchisor_id: str) -> Optional[FranchisorResponse]:
        """
        Activate a franchisor.
        
        Args:
            franchisor_id: Franchisor UUID
            
        Returns:
            Updated franchisor response or None if not found
        """
        try:
            updated_franchisor = await self.repository.update_status(franchisor_id, is_active=True)
            if not updated_franchisor:
                return None
            
            return await self._format_franchisor_response(updated_franchisor)
            
        except Exception as e:
            logger.error(f"Error activating franchisor: {e}")
            raise HTTPException(status_code=500, detail="Failed to activate franchisor")
    
    async def deactivate_franchisor(self, franchisor_id: str) -> Optional[FranchisorResponse]:
        """
        Deactivate a franchisor.
        
        Args:
            franchisor_id: Franchisor UUID
            
        Returns:
            Updated franchisor response or None if not found
        """
        try:
            updated_franchisor = await self.repository.update_status(franchisor_id, is_active=False)
            if not updated_franchisor:
                return None
            
            return await self._format_franchisor_response(updated_franchisor)
            
        except Exception as e:
            logger.error(f"Error deactivating franchisor: {e}")
            raise HTTPException(status_code=500, detail="Failed to deactivate franchisor")
    
    async def delete_franchisor(self, franchisor_id: str) -> bool:
        """
        Soft delete a franchisor.
        
        Args:
            franchisor_id: Franchisor UUID
            
        Returns:
            True if deleted, False if not found
        """
        try:
            return await self.repository.soft_delete(franchisor_id)
            
        except Exception as e:
            logger.error(f"Error deleting franchisor: {e}")
            raise HTTPException(status_code=500, detail="Failed to delete franchisor")
    
    async def _format_franchisor_response(self, franchisor: Franchisor) -> FranchisorResponse:
        """
        Format franchisor model to response schema.
        
        Args:
            franchisor: Franchisor model instance
            
        Returns:
            Formatted franchisor response
        """
        # Get industry details
        industry_details = await self._get_industry_details(franchisor.industry_id)
        
        # Process brochure URL - convert S3 key to full URL
        brochure_url = franchisor.brochure_url
        if brochure_url and self.s3_service:
            # If it's just a key/filename, convert to full URL
            if not brochure_url.startswith('http'):
                brochure_url = self.s3_service.get_full_s3_url(brochure_url)
        
        return FranchisorResponse(
            id=str(franchisor.id),
            name=franchisor.name,
            contactFirstName=franchisor.contactfirstname,
            contactLastName=franchisor.contactlastname,
            email=franchisor.email,
            phone=franchisor.phone,
            industry_id=str(franchisor.industry_id) if franchisor.industry_id else None,
            industry_details=industry_details,
            region=franchisor.region,
            budget=franchisor.budget,
            brochure_url=brochure_url,
            franchisor_won_id=franchisor.franchisor_won_id,
            is_active=franchisor.is_active,
            is_deleted=franchisor.is_deleted,
            created_at=franchisor.created_at,
            updated_at=franchisor.updated_at
        )

    async def _get_industry_details(self, industry_id: Optional[str]) -> Optional[IndustryResponse]:
        """Get industry details by ID."""
        if not industry_id:
            return None

        try:
            from app.repositories.industry_repository import IndustryRepository
            from app.schemas.industry import IndustryResponse

            # Use the same database session as the franchisor repository
            industry_repo = IndustryRepository(self.repository.db)
            industry = await industry_repo.get(industry_id)
            if industry:
                return IndustryResponse(
                    id=str(industry.id),
                    name=industry.name,
                    description=industry.description,
                    is_active=industry.is_active,
                    is_deleted=industry.is_deleted,
                    created_at=industry.created_at,
                    updated_at=industry.updated_at
                )
        except Exception as e:
            logger.error(f"Error getting industry details: {e}")

        return None

    async def _process_brochure_with_agents(self, franchisor_id: str, brochure_filename: str):
        """
        Process brochure document using the agent system for ingestion

        Args:
            franchisor_id: ID of the franchisor
            brochure_filename: S3 filename of the uploaded brochure
        """
        try:
            from app.agents.orchestrator import AgentOrchestrator
            from app.core.config.settings import settings

            # Initialize agent orchestrator
            orchestrator = AgentOrchestrator()

            # Construct S3 URL for the document
            s3_base_url = settings.S3_BASE_URL or f"https://{settings.S3_BUCKET_NAME}.s3.{settings.AWS_REGION}.amazonaws.com"
            document_url = f"{s3_base_url}/{brochure_filename}"

            # Process document through agent system
            context = {
                "document_url": document_url,
                "document_path": brochure_filename,
                "franchisor_id": franchisor_id,
                "document_type": "brochure",
                "source": "franchisor_upload"
            }

            result = await orchestrator.process_message(
                message=f"Please process the uploaded brochure document: {brochure_filename}",
                session_id=f"franchisor_{franchisor_id}",
                context=context
            )

            if result.get("success"):
                logger.info(f"Successfully processed brochure for franchisor {franchisor_id} through agent system")
            else:
                logger.error(f"Failed to process brochure for franchisor {franchisor_id}: {result.get('error')}")

        except Exception as e:
            logger.error(f"Error processing brochure with agents for franchisor {franchisor_id}: {str(e)}")
            # Don't raise exception to avoid breaking the main upload flow
