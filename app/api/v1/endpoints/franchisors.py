"""
Franchisor management endpoints with full Swagger documentation
"""

import csv
import io
from typing import Optional, Annotated
from fastapi import APIRouter, Depends, Query, Path, Header, UploadFile, File, Form, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.franchisor import (
    FranchisorCreateRequest,
    FranchisorUpdateRequest,
    FranchisorListResponse,
    FranchisorSuccessResponse,
    FranchisorListSuccessResponse,
    FranchisorImportSuccessResponse,
    FranchisorDeleteSuccessResponse
)

from app.schemas.base_response import (
    COMMON_RESPONSES,
    ResponseMessage,
    PaginationInfo
)
from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.factory import get_franchisor_service
from app.services.franchisor_service import FranchisorService
from app.services.s3_service import S3Service
from app.core.logging import logger
from app.schemas.user import UserBase

router = APIRouter()


@router.get(
    "/industries",
    summary="Get Available Industries",
    description="Get list of available industries for franchisor creation/update",
    responses={
        200: {
            "description": "Industries retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Industries Retrieved",
                            "description": "Available industries retrieved successfully"
                        },
                        "data": [
                            {
                                "id": 1,
                                "name": "Food & Beverage",
                                "description": "All food-related businesses",
                                "is_active": True,
                                "is_deleted": False,
                                "created_at": "2024-01-01T00:00:00Z",
                                "updated_at": "2024-01-01T00:00:00Z"
                            }
                        ]
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_franchisor_industries(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of available industries for franchisor creation/update"""
    try:
        from app.repositories.industry_repository import IndustryRepository
        from app.services.industry_service import IndustryService
        from sqlalchemy.exc import SQLAlchemyError
        from fastapi import HTTPException, status

        # Initialize repository and service with proper error handling
        try:
            industry_repository = IndustryRepository(db)
            industry_service = IndustryService(industry_repository)
        except Exception as init_error:
            logger.error(f"Failed to initialize industry service: {init_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Service initialization failed"
            )

        # Call service method with proper error handling
        try:
            result = await industry_service.list_industries(skip=0, limit=1000)
        except SQLAlchemyError as db_error:
            logger.error(f"Database error retrieving industries: {db_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database operation failed"
            )
        except Exception as service_error:
            logger.error(f"Service error retrieving industries: {service_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Service operation failed"
            )

        # Handle service response - check if it's an error response
        if isinstance(result, dict) and not result.get("success", True):
            logger.error("Industry service returned an error response")
            return create_error_response(
                error_code=ErrorCodes.DATABASE_ERROR,
                message_title="Error Retrieving Industries",
                message_description="An error occurred while retrieving industries",
                status_code=500
            )

        # Handle the new service signature that returns tuple (industries, total_count)
        if isinstance(result, tuple):
            industries, _ = result
        else:
            industries = result

        # Validate that industries is a list
        if not isinstance(industries, list):
            logger.error(f"Expected list of industries, got {type(industries)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid service response format"
            )

        # Convert SQLAlchemy models to simple dict format with proper validation
        industry_list = []
        for industry in industries:
            try:
                # Validate industry object has required attributes
                if not hasattr(industry, 'id') or not hasattr(industry, 'name'):
                    logger.error("Invalid industry object: missing required attributes")
                    continue

                industry_dict = {
                    "id": str(industry.id) if industry.id else None,
                    "name": industry.name if hasattr(industry, 'name') else "",
                    "description": industry.description if hasattr(industry, 'description') else "",
                    "is_active": industry.is_active if hasattr(industry, 'is_active') else True,
                    "is_deleted": industry.is_deleted if hasattr(industry, 'is_deleted') else False,
                    "created_at": industry.created_at if hasattr(industry, 'created_at') else None,
                    "updated_at": industry.updated_at if hasattr(industry, 'updated_at') else None
                }
                industry_list.append(industry_dict)
            except Exception as conversion_error:
                logger.error(f"Error converting industry to dict: {conversion_error}")
                continue

        return create_success_response(
            data=industry_list,
            message_title="Industries Retrieved",
            message_description="Available industries retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error retrieving industries: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Industries",
            message_description="An error occurred while retrieving industries",
            status_code=500
        )





@router.get(
    "/",
    response_model=FranchisorListSuccessResponse,
    summary="List Franchisors",
    description="Retrieve a paginated list of franchisors with optional filtering, sorting, and name search",
    responses={
        200: {
            "description": "Franchisors retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisors Retrieved",
                            "description": "Franchisors retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "frc_123456789",
                                    "name": "Sample Franchisor",
                                    "category": "food_beverage",
                                    "region": "north_america",
                                    "budget": 150000.0,
                                    "sub_category": "restaurant",
                                    "brochure_url": "https://example.com/brochure.pdf",
                                    "is_active": True,
                                    "created_at": "2024-01-01T00:00:00Z",
                                    "updated_at": "2024-01-01T00:00:00Z"
                                }
                            ],
                            "total_count": 1,
                            "pagination": {
                                "current_page": 1,
                                "total_pages": 1,
                                "items_per_page": 20,
                                "total_items": 1
                            }
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def list_franchisors(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    category: Optional[str] = Query(None, description="Filter by category"),
    region: Optional[str] = Query(None, description="Filter by region"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search franchisors by name (case-insensitive)"),
    current_user: UserBase = Depends(get_current_user),
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """List franchisors with pagination, filtering, and name search"""
    try:
        franchisors, total_count = await franchise_service.get_franchisors(
            skip=skip,
            limit=limit,
            category=category,
            region=region,
            is_active=is_active,
            search=search
        )
        
        # Calculate pagination info
        page = (skip // limit) + 1
        pages = (total_count + limit - 1) // limit
        
        return FranchisorListSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Franchisors Retrieved",
                description="Franchisors retrieved successfully"
            ),
            data=FranchisorListResponse(
                items=franchisors,
                total_count=total_count,
                pagination=PaginationInfo(
                    current_page=page,
                    total_pages=pages,
                    items_per_page=limit,
                    total_items=total_count
                )
            )
        )
        
    except Exception as e:
        logger.error(f"Error in list_franchisors: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve franchisors"
        )



@router.get(
    "/{franchisor_id}",
    response_model=FranchisorSuccessResponse,
    summary="Get Franchisor by ID",
    description="Retrieve a specific franchisor by its unique identifier",
    responses={
        200: {
            "description": "Franchisor retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Retrieved",
                            "description": "Franchisor retrieved successfully"
                        },
                        "data": {
                            "id": "frc_123456789",
                            "name": "Coffee Club Melbourne",
                            "category": "food_beverage",
                            "region": "australia",
                            "budget": 250000.0,
                            "sub_category": "cafe",
                            "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    current_user: UserBase = Depends(get_current_user),
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Retrieve a specific franchisor by its unique identifier.
    
    This endpoint returns detailed information about a franchisor including
    all its properties and metadata.
    """
    try:
        franchisor = await franchise_service.get_franchisor_by_id(franchisor_id)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        return create_success_response(
            data=franchisor.model_dump(),
            message_title="Franchisor Retrieved",
            message_description="Franchisor retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error retrieving franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Franchisor",
            message_description="An error occurred while retrieving the franchisor",
            status_code=500
        )


@router.post(
    "/",
    response_model=FranchisorSuccessResponse,
    summary="Create New Franchisor",
    description="Create a new franchisor with comprehensive validation and authentication",
    responses={
        201: {
            "description": "Franchisor created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Created",
                            "description": "Franchisor created successfully"
                        },
                        "data": {
                            "id": "frc_987654321",
                            "name": "Jim's Mowing Sydney",
                            "category": "home_services",
                            "region": "australia",
                            "budget": 35000.0,
                            "sub_category": "lawn_care",
                            "brochure_url": None,
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T00:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    status_code=201,
    dependencies=[Depends(get_current_user)]
)
async def create_franchisor(
    franchisor_data: FranchisorCreateRequest,
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Create a new franchisor with comprehensive validation.

    This endpoint requires authentication and validates all input data
    before creating the franchisor record.
    """
    try:
        franchisor = await franchise_service.create_franchisor(franchisor_data)

        # Get the full franchisor response with relationship data
        franchisor_response = await franchise_service.get_franchisor_by_id(str(franchisor.id))

        return create_success_response(
            data=franchisor_response.model_dump() if franchisor_response else None,
            message_title="Franchisor Created",
            message_description="Franchisor created successfully"
        )

    except Exception as e:
        import traceback
        logger.error(f"Error creating franchisor: {e}\n{traceback.format_exc()}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Creating Franchisor",
            message_description="An error occurred while creating the franchisor",
            status_code=500
        )


@router.put(
    "/{franchisor_id}",
    summary="Update Franchisor",
    description="Update an existing franchisor with optional brochure upload",
    responses={
        200: {
            "description": "Franchisor updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Updated",
                            "description": "Franchisor updated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def update_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    name: Optional[str] = Form(None, description="Franchisor name"),

    # UUID-based category relationships
    category_id: Optional[str] = Form(None, description="Category UUID from category table"),
    subcategory_id: Optional[str] = Form(None, description="Subcategory UUID from subcategory table"),

    region: Optional[str] = Form(None, description="Franchisor region"),
    budget: Optional[float] = Form(None, description="Franchisor budget"),
    is_active: Optional[bool] = Form(None, description="Franchisor active status"),
    brochure_file: Optional[UploadFile] = File(None, description="Optional brochure file (PDF only)"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Update an existing franchisor with comprehensive validation and optional brochure upload.
    
    This endpoint requires authentication and validates all input data
    before updating the franchisor record. If a brochure file is provided,
    it will be uploaded to S3 and the franchisor's brochure_url will be updated.
    """
    try:
        # Create FranchisorUpdateRequest from form data, only including non-None values
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if category_id is not None:
            update_data["category_id"] = category_id
        if subcategory_id is not None:
            update_data["subcategory_id"] = subcategory_id
        if region is not None:
            update_data["region"] = region
        if budget is not None:
            update_data["budget"] = budget
        if is_active is not None:
            update_data["is_active"] = is_active

        franchisor_data = FranchisorUpdateRequest(**update_data)
        
        franchisor = await franchise_service.update_franchisor(franchisor_id, franchisor_data, brochure_file)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        message_description = "Franchisor updated successfully"
        if brochure_file:
            message_description += " with new brochure"

        return create_success_response(
            data=None,
            message_title="Franchisor Updated",
            message_description=message_description
        )

    except Exception as e:
        logger.error(f"Error updating franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Franchisor",
            message_description="An error occurred while updating the franchisor",
            status_code=500
        )


@router.delete(
    "/{franchisor_id}",
    response_model=FranchisorDeleteSuccessResponse,
    summary="Delete Franchisor",
    description="Delete a franchisor with authentication and authorization checks",
    responses={
        200: {
            "description": "Franchisor deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Deleted",
                            "description": "Franchisor deleted successfully"
                        },
                        "data": {
                            "franchisor_id": "frc_123456789",
                            "deleted_at": "2024-01-01T12:00:00Z",
                            "message": "Franchisor has been permanently deleted"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def delete_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Delete a franchisor with authentication and authorization checks.
    
    This endpoint requires authentication and will permanently delete
    the franchisor record from the database.
    """
    try:
        success = await franchise_service.delete_franchisor(franchisor_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        from datetime import datetime
        response_data = {
            "franchisor_id": franchisor_id,
            "deleted_at": datetime.utcnow(),
            "message": "Franchisor has been permanently deleted"
        }

        return create_success_response(
            data=response_data,
            message_title="Franchisor Deleted",
            message_description="Franchisor deleted successfully"
        )

    except Exception as e:
        logger.error(f"Error deleting franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deleting Franchisor",
            message_description="An error occurred while deleting the franchisor",
            status_code=500
        )


@router.patch(
    "/{franchisor_id}/activate",
    summary="Activate Franchisor",
    description="Activate a franchisor by setting is_active=True",
    responses={
        200: {
            "description": "Franchisor activated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Activated",
                            "description": "Franchisor activated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def activate_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier")],
    current_user: UserBase = Depends(get_current_user),
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Activate a franchisor by setting is_active=True.

    This endpoint requires authentication and will set the franchisor's
    is_active status to True, making it available for operations.
    """
    try:
        franchisor = await franchise_service.activate_franchisor(franchisor_id)

        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        return create_success_response(
            data=franchisor.model_dump(),
            message_title="Franchisor Activated",
            message_description="Franchisor activated successfully"
        )

    except Exception as e:
        logger.error(f"Error activating franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Activating Franchisor",
            message_description="An error occurred while activating the franchisor",
            status_code=500
        )


@router.patch(
    "/{franchisor_id}/deactivate",
    summary="Deactivate Franchisor",
    description="Deactivate a franchisor by setting is_active=False",
    responses={
        200: {
            "description": "Franchisor deactivated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Deactivated",
                            "description": "Franchisor deactivated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def deactivate_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier")],
    current_user: UserBase = Depends(get_current_user),
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Deactivate a franchisor by setting is_active=False.

    This endpoint requires authentication and will set the franchisor's
    is_active status to False, making it unavailable for operations.
    """
    try:
        franchisor = await franchise_service.deactivate_franchisor(franchisor_id)

        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        return create_success_response(
            data=franchisor.model_dump(),
            message_title="Franchisor Deactivated",
            message_description="Franchisor deactivated successfully"
        )

    except Exception as e:
        logger.error(f"Error deactivating franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deactivating Franchisor",
            message_description="An error occurred while deactivating the franchisor",
            status_code=500
        )


@router.post(
    "/import",
    response_model=FranchisorImportSuccessResponse,
    summary="Import Franchisors from CSV",
    description="Bulk import franchisors from CSV file with validation",
    responses={
        200: {
            "description": "CSV import completed",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "CSV Import Completed",
                            "description": "CSV import completed successfully"
                        },
                        "data": {
                            "total_rows": 10,
                            "successful_imports": 8,
                            "failed_imports": 2,
                            "errors": [
                                {
                                    "row": 3,
                                    "field": "name",
                                    "message": "Name is required"
                                }
                            ]
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def import_franchisors(
    file: UploadFile = File(..., description="CSV file containing franchisor data"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Import franchisors from a CSV file with comprehensive validation.
    
    The CSV file should contain the following columns:
    - name (required): Franchisor name
    - category (required): Franchisor category
    - region (optional): Franchisor region
    - budget (optional): Budget amount
    - sub_category (optional): Sub-category
    - is_active (optional): Active status (true/false)
    """
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid File Type",
                message_description="Only CSV files are allowed for import",
                status_code=400
            )
        
        # Read CSV content
        content = await file.read()
        csv_text = content.decode('utf-8')
        
        # Parse CSV
        csv_reader = csv.DictReader(io.StringIO(csv_text))
        rows = list(csv_reader)
        
        if not rows:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Empty CSV File",
                message_description="The CSV file contains no data",
                status_code=400
            )
        
        successful_imports = 0
        failed_imports = 0
        errors = []
        
        # Process each row
        for row_num, row in enumerate(rows, start=2):  # Start from 2 because row 1 is header
            try:
                # Validate required fields
                if not row.get('name'):
                    errors.append({
                        "row": row_num,
                        "field": "name",
                        "message": "Name is required"
                    })
                    failed_imports += 1
                    continue
                
                if not row.get('category'):
                    errors.append({
                        "row": row_num,
                        "field": "category",
                        "message": "Category is required"
                    })
                    failed_imports += 1
                    continue

                # Validate category (now using string)
                category = row.get('category', '').strip()
                if not category:
                    errors.append({
                        "row": row_num,
                        "field": "category",
                        "message": "Category is required"
                    })
                    failed_imports += 1
                    continue
                
                # Validate region if provided (now using string)
                region = row.get('region', '').strip() or None
                
                # Validate budget if provided
                budget = None
                if row.get('budget'):
                    try:
                        budget = float(row['budget'])
                        if budget < 0:
                            raise ValueError("Budget cannot be negative")
                    except ValueError:
                        errors.append({
                            "row": row_num,
                            "field": "budget",
                            "message": f"Invalid budget: {row['budget']}"
                        })
                        failed_imports += 1
                        continue
                

                
                # Validate is_active if provided
                is_active = True
                if row.get('is_active'):
                    is_active_str = row['is_active'].lower()
                    if is_active_str in ['true', '1', 'yes']:
                        is_active = True
                    elif is_active_str in ['false', '0', 'no']:
                        is_active = False
                    else:
                        errors.append({
                            "row": row_num,
                            "field": "is_active",
                            "message": f"Invalid is_active value: {row['is_active']}"
                        })
                        failed_imports += 1
                        continue
                
                # Create franchisor data
                franchisor_data = {
                    "name": row['name'],
                    "category_id": "07a64d9b-1f6f-4fe4-aa76-84de693d2e06",  # Default category ID for CSV import
                    "subcategory_id": "45ffacff-3f59-496e-ba14-4e0e246e7c75",  # Default subcategory ID for CSV import
                    "region": region,
                    "budget": budget,
                    "is_active": is_active
                }
                
                # Create franchisor
                await franchise_service.create_franchisor(
                    FranchisorCreateRequest(**franchisor_data)
                )
                successful_imports += 1
                
            except Exception as e:
                errors.append({
                    "row": row_num,
                    "field": "general",
                    "message": f"Error processing row: {str(e)}"
                })
                failed_imports += 1
        
        response_data = {
            "total_rows": len(rows),
            "successful_imports": successful_imports,
            "failed_imports": failed_imports,
            "errors": errors
        }
        
        return create_success_response(
            data=response_data,
            message_title="CSV Import Completed",
            message_description=f"Successfully imported {successful_imports} franchisors"
        )
        
    except Exception as e:
        logger.error(f"Error importing franchisors from CSV: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Importing Franchisors",
            message_description="An error occurred while importing franchisors from CSV",
            status_code=500
        )


@router.post(
    "/{franchisor_id}/upload-brochure",
    response_model=FranchisorSuccessResponse,
    summary="Upload Franchisor Brochure",
    description="Upload a brochure file for a franchisor with background processing",
    responses={
        200: {
            "description": "Brochure uploaded successfully and queued for processing",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Brochure Uploaded",
                            "description": "Brochure uploaded successfully and queued for background processing"
                        },
                        "data": {
                            "id": "frc_123456789",
                            "name": "Coffee Club Melbourne",
                            "category": "food_beverage",
                            "region": "australia",
                            "budget": 250000.0,
                            "sub_category": "cafe",
                            "brochure_url": "https://s3.amazonaws.com/bucket/brochures/20240101_123456_abc123.pdf",
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z",
                            "processing_task_id": "task_123456789",
                            "processing_status": "queued"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def upload_brochure(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    file: UploadFile = File(..., description="Brochure file (PDF only)"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    franchise_service: FranchisorService = Depends(get_franchisor_service)
):
    """
    Upload a brochure file for a franchisor with background processing.

    This endpoint:
    1. Uploads the PDF file to S3
    2. Updates the franchisor's brochure_url
    3. Queues the document for background processing using Celery/RabbitMQ
    4. Returns immediately with task ID for status tracking

    The document processing (ingestion, embedding generation, etc.) happens
    asynchronously in the background. Use the returned task_id to check
    processing status via /api/background-tasks/status/{task_id}

    Supported file types: PDF only
    Maximum file size: 10MB
    """
    try:
        # Check if franchisor exists
        franchisor = await franchise_service.get_franchisor_by_id(franchisor_id)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )
        
        # Upload file to S3
        s3_service = S3Service()
        brochure_filename = await s3_service.upload_file(file, prefix="brochures")
        
        # Update franchisor with brochure filename
        updated_franchisor = await franchise_service.update_brochure_url(franchisor_id, brochure_filename)
        
        if not updated_franchisor:
            return create_error_response(
                error_code=ErrorCodes.DATABASE_ERROR,
                message_title="Error Updating Franchisor",
                message_description="Failed to update franchisor with brochure filename",
                status_code=500
            )
        
        # Get the updated franchisor with proper response format
        updated_franchisor_response = await franchise_service.get_franchisor_by_id(franchisor_id)

        # Queue document processing in background using Celery
        try:
            from app.services.background_task_manager import BackgroundTaskManager
            from app.core.config.settings import settings

            # Initialize background task manager
            task_manager = BackgroundTaskManager()

            # Construct S3 URL for the document
            s3_base_url = settings.S3_BASE_URL or f"https://{settings.S3_BUCKET_NAME}.s3.{settings.AWS_REGION}.amazonaws.com"
            document_url = f"{s3_base_url}/{brochure_filename}"

            # Queue DocQA processing task
            processing_options = {
                "document_type": "brochure",
                "source": "brochure_upload_endpoint",
                "user_id": current_user.get("id") if isinstance(current_user, dict) else str(current_user.id),
                "filename": file.filename,
                "request_id": x_request_id
            }

            task_id = task_manager.queue_docqa_processing(
                franchisor_id=franchisor_id,
                brochure_url=document_url,
                processing_options=processing_options
            )

            logger.info(f"Queued brochure processing for franchisor {franchisor_id}, task_id: {task_id}")

            # Prepare response data with task information
            response_data = updated_franchisor_response.model_dump() if updated_franchisor_response else {}
            response_data.update({
                "processing_task_id": task_id,
                "processing_status": "queued",
                "processing_message": "Document queued for background processing"
            })

            return create_success_response(
                data=response_data,
                message_title="Brochure Uploaded",
                message_description=f"PDF brochure uploaded successfully and queued for background processing. Task ID: {task_id}"
            )

        except Exception as e:
            # Log error but don't fail the upload since file is already uploaded
            logger.error(f"Failed to queue brochure processing for franchisor {franchisor_id}: {e}")

            # Return success with warning about processing
            response_data = updated_franchisor_response.model_dump() if updated_franchisor_response else {}
            response_data.update({
                "processing_task_id": None,
                "processing_status": "failed_to_queue",
                "processing_message": "File uploaded but failed to queue for processing"
            })

            return create_success_response(
                data=response_data,
                message_title="Brochure Uploaded",
                message_description="PDF brochure uploaded successfully but processing could not be queued"
            )
        
    except Exception as e:
        logger.error(f"Error uploading brochure for franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Uploading Brochure",
            message_description="An error occurred while uploading the brochure",
            status_code=500
        )


