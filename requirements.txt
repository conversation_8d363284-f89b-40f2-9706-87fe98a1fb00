# GrowthHive Consolidated Requirements
# Merged from: requirements.txt, docqa/requirements.txt, ingest_requirements.txt

# ===== CORE FRAMEWORK =====
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# ===== DATABASE & ORM =====
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0
greenlet>=3.0.0
psycopg[binary]>=3.1.0
pgvector>=0.4.0
psycopg2-binary>=2.9.0

# ===== AUTHENTICATION & SECURITY =====
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
bcrypt>=4.0.1
python-multipart>=0.0.6
PyJWT>=2.8.0

# ===== DATA VALIDATION & SETTINGS =====
pydantic>=2.5.0
pydantic-settings>=2.1.0
email-validator>=2.0.0

# ===== HTTP CLIENT & REQUESTS =====
httpx>=0.25.0
requests>=2.31.0

# ===== FILE HANDLING & STORAGE =====
aiofiles>=24.1.0
boto3>=1.34.0
botocore>=1.34.0

# ===== AI & MACHINE LEARNING =====
openai>=1.3.0
numpy>=1.24.0
scikit-learn>=1.3.0
tiktoken>=0.5.0

# ===== MULTI-AGENT FRAMEWORK =====
langgraph>=0.0.55
langgraph-checkpoint>=1.0.0
langchain>=0.1.0
langchain-openai>=0.0.8
langchain-community>=0.0.20
langchain-core>=0.1.0

# ===== VECTOR DATABASE & EMBEDDINGS =====
faiss-cpu>=1.7.4
chromadb>=0.4.0

# ===== MEMORY & CACHING =====
redis>=5.0.0
redis-py>=5.0.0

# ===== BACKGROUND TASK PROCESSING =====
celery>=5.3.0
kombu>=5.3.0

# ===== DOCUMENT PROCESSING =====
PyMuPDF>=1.23.0
python-docx>=0.8.11
python-pptx>=0.6.21
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0
beautifulsoup4>=4.12.0
readability-lxml>=0.8.1
unstructured[all-docs]>=0.11.0
textract>=1.6.5

# ===== IMAGE PROCESSING & OCR =====
Pillow>=10.0.0
opencv-python>=4.8.0
pytesseract>=0.3.10

# ===== LANGUAGE PROCESSING =====
langdetect>=1.0.9

# ===== FILE TYPE DETECTION =====
python-magic>=0.4.27
chardet>=5.0.0

# ===== CLI & UI =====
typer[all]>=0.9.0
rich>=13.0.0
click>=8.0.0

# ===== UTILITIES =====
python-dotenv>=1.0.0
python-dateutil>=2.8.2
pytz>=2023.3
tenacity>=8.0.0

# ===== TESTING =====
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
moto[s3]>=4.2.0

# ===== DEVELOPMENT & CODE QUALITY =====
ruff>=0.1.0
mypy>=1.7.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# ===== LOGGING & MONITORING =====
structlog>=23.2.0

# ===== PHONE NUMBER VALIDATION =====
phonenumbers>=8.13.0

# ===== OPTIONAL DEPENDENCIES (COMMENTED) =====
# Advanced PDF processing
# tabula-py>=2.7.0
# camelot-py[cv]>=0.10.1
# pdfplumber>=0.9.0

# Advanced OCR engines
# easyocr>=1.7.0
# paddleocr>=2.7.0

# Advanced NLP
# spacy>=3.6.0
# transformers>=4.30.0

# Alternative vector databases
# pinecone-client>=2.2.0
# weaviate-client>=3.22.0

# Monitoring and observability
# prometheus-client>=0.19.0
# sentry-sdk>=1.28.0